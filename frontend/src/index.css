@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

@theme {
  --color-border: #e5e7eb;
  --color-background: #ffffff;
  --color-foreground: #111827;
  --color-ring: #3b82f6;
}

@layer base {
  * {
    border-color: var(--color-border);
  }

  body {
    background-color: var(--color-background);
    color: var(--color-foreground);
    font-family: 'Inter', system-ui, sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* POS specific components */
  .pos-button {
    @reference;
    @apply inline-flex items-center justify-center rounded-lg px-4 py-2 text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50;
  }

  .pos-button-primary {
    @reference;
    @apply pos-button bg-primary-600 text-white hover:bg-primary-700;
  }

  .pos-button-secondary {
    @reference;
    @apply pos-button bg-secondary-200 text-secondary-800 hover:bg-secondary-300;
  }

  .pos-button-success {
    @reference;
    @apply pos-button bg-success-500 text-white hover:bg-success-600;
  }

  .pos-button-warning {
    @reference;
    @apply pos-button bg-warning-500 text-white hover:bg-warning-600;
  }

  .pos-button-error {
    @reference;
    @apply pos-button bg-error-500 text-white hover:bg-error-600;
  }

  .pos-card {
    @reference;
    @apply rounded-lg border bg-white shadow-sm;
  }

  .pos-input {
    @reference;
    @apply flex h-10 w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .menu-item-card {
    @reference;
    @apply pos-card p-4 cursor-pointer transition-all hover:shadow-md hover:scale-105 active:scale-95;
  }

  .order-item {
    @reference;
    @apply flex items-center justify-between p-3 border-b border-gray-200 last:border-b-0;
  }

  .kitchen-order-card {
    @reference;
    @apply pos-card p-4 m-2 min-w-80 max-w-sm;
  }

  .table-button {
    @reference;
    @apply pos-card p-4 cursor-pointer transition-all hover:shadow-md text-center min-h-24 flex flex-col items-center justify-center;
  }

  .table-available {
    @reference;
    @apply table-button bg-green-50 border-green-200 text-green-800 hover:bg-green-100;
  }

  .table-occupied {
    @reference;
    @apply table-button bg-red-50 border-red-200 text-red-800 hover:bg-red-100;
  }

  .table-reserved {
    @reference;
    @apply table-button bg-yellow-50 border-yellow-200 text-yellow-800 hover:bg-yellow-100;
  }
}
