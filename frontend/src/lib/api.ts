/**
 * API client configuration for Restaurant POS system
 */

import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token management
const getToken = (): string | null => {
  return localStorage.getItem('access_token');
};

const setToken = (token: string): void => {
  localStorage.setItem('access_token', token);
};

const removeToken = (): void => {
  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
};

const getRefreshToken = (): string | null => {
  return localStorage.getItem('refresh_token');
};

const setRefreshToken = (token: string): void => {
  localStorage.setItem('refresh_token', token);
};

// Restaurant ID management
const getRestaurantId = (): string | null => {
  return localStorage.getItem('restaurant_id');
};

const setRestaurantId = (restaurantId: string): void => {
  localStorage.setItem('restaurant_id', restaurantId);
};

// Request interceptor to add auth token and restaurant ID
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    const token = getToken();
    const restaurantId = getRestaurantId();
    
    if (token) {
      config.headers = {
        ...config.headers,
        Authorization: `Bearer ${token}`,
      };
    }
    
    if (restaurantId) {
      config.headers = {
        ...config.headers,
        'X-Restaurant-ID': restaurantId,
      };
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      const refreshToken = getRefreshToken();
      if (refreshToken) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/token/refresh/`, {
            refresh: refreshToken,
          });
          
          const { access } = response.data;
          setToken(access);
          
          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access}`;
          return api(originalRequest);
        } catch (refreshError) {
          // Refresh failed, redirect to login
          removeToken();
          window.location.href = '/login';
          return Promise.reject(refreshError);
        }
      } else {
        // No refresh token, redirect to login
        removeToken();
        window.location.href = '/login';
      }
    }
    
    // Handle other errors
    if (error.response?.data?.message) {
      toast.error(error.response.data.message);
    } else if (error.message) {
      toast.error(error.message);
    }
    
    return Promise.reject(error);
  }
);

// API endpoints
export const authAPI = {
  login: (credentials: { email: string; password: string; restaurant_id?: string }) =>
    api.post('/auth/login/', credentials),
  
  register: (userData: {
    email: string;
    username: string;
    first_name: string;
    last_name: string;
    password: string;
    password_confirm: string;
    user_type: string;
  }) => api.post('/auth/register/', userData),
  
  logout: (data: { refresh: string; session_id?: string }) =>
    api.post('/auth/logout/', data),
  
  refreshToken: (refresh: string) =>
    api.post('/auth/token/refresh/', { refresh }),
  
  getProfile: () => api.get('/auth/profile/'),
  
  updateProfile: (data: any) => api.patch('/auth/profile/', data),
  
  changePassword: (data: {
    old_password: string;
    new_password: string;
    new_password_confirm: string;
  }) => api.post('/auth/password/change/', data),
  
  requestPasswordReset: (email: string) =>
    api.post('/auth/password/reset/', { email }),
  
  confirmPasswordReset: (data: {
    token: string;
    new_password: string;
    new_password_confirm: string;
  }) => api.post('/auth/password/reset/confirm/', data),
  
  verifyEmail: (token: string) =>
    api.post('/auth/verify-email/', { token }),
};

export const menuAPI = {
  getCategories: () => api.get('/menu/categories/'),
  getItems: (params?: any) => api.get('/menu/items/', { params }),
  getItem: (id: string) => api.get(`/menu/items/${id}/`),
  createItem: (data: any) => api.post('/menu/items/', data),
  updateItem: (id: string, data: any) => api.patch(`/menu/items/${id}/`, data),
  deleteItem: (id: string) => api.delete(`/menu/items/${id}/`),
};

export const ordersAPI = {
  getOrders: (params?: any) => api.get('/orders/', { params }),
  getOrder: (id: string) => api.get(`/orders/${id}/`),
  createOrder: (data: any) => api.post('/orders/', data),
  updateOrder: (id: string, data: any) => api.patch(`/orders/${id}/`, data),
  deleteOrder: (id: string) => api.delete(`/orders/${id}/`),
  addItem: (orderId: string, data: any) => api.post(`/orders/${orderId}/items/`, data),
  updateItem: (orderId: string, itemId: string, data: any) =>
    api.patch(`/orders/${orderId}/items/${itemId}/`, data),
  removeItem: (orderId: string, itemId: string) =>
    api.delete(`/orders/${orderId}/items/${itemId}/`),
};

export const tablesAPI = {
  getTables: () => api.get('/restaurants/tables/'),
  getTable: (id: string) => api.get(`/restaurants/tables/${id}/`),
  updateTable: (id: string, data: any) => api.patch(`/restaurants/tables/${id}/`, data),
};

export const inventoryAPI = {
  getItems: (params?: any) => api.get('/inventory/items/', { params }),
  getItem: (id: string) => api.get(`/inventory/items/${id}/`),
  updateStock: (id: string, data: any) => api.patch(`/inventory/items/${id}/stock/`, data),
  getSuppliers: () => api.get('/inventory/suppliers/'),
  getPurchaseOrders: () => api.get('/inventory/purchase-orders/'),
};

export const billingAPI = {
  processPayment: (data: any) => api.post('/billing/payments/', data),
  generateReceipt: (orderId: string) => api.get(`/billing/receipts/${orderId}/`),
  getPaymentMethods: () => api.get('/billing/payment-methods/'),
};

export const analyticsAPI = {
  getSalesReport: (params: any) => api.get('/analytics/sales/', { params }),
  getInventoryReport: (params: any) => api.get('/analytics/inventory/', { params }),
  getStaffReport: (params: any) => api.get('/analytics/staff/', { params }),
  getDashboard: () => api.get('/analytics/dashboard/'),
};

// Export utilities
export {
  api as default,
  getToken,
  setToken,
  removeToken,
  getRefreshToken,
  setRefreshToken,
  getRestaurantId,
  setRestaurantId,
};
